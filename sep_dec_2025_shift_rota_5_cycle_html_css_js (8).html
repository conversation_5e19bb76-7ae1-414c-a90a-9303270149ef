<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Shift Schedule – Dynamic Yearly Rota</title>
  <style>
    body{font-family:Arial, sans-serif;color:#000;margin:0;background:#fff;}
    header{text-align:center;margin:10px 0;}
    h1{font-size:16px;margin:0;}
    h2{font-size:13px;margin:4px 0;}
    header div{font-size:12px;margin-bottom:8px;}

    .container{width:100%;padding:0 8px;display:flex;justify-content:center;}

    table{border-collapse:collapse;font-size:11px;table-layout:fixed;min-width:1000px;margin:0 auto;}
    th, td{border:1px solid #000;text-align:center;padding:2px;}
    thead th{background:#d9d9d9;font-weight:bold;font-size:10px;}

    .month-cell{font-weight:bold;text-align:center;background:#cfd8dc;font-size:11px;letter-spacing:1px;}
    .month-row td{border-top:2px solid #000;background:#e8eef3;font-weight:bold;}
    .month-row .daynum{background:#b0bec5;color:#000;}

    .shift-name{font-weight:bold;text-align:left;background:#ddebf7;}
    .daynum{font-weight:bold;font-size:9px;background:#f1f1f1;}

    .weekend{background:#fff2cc;}
    .holiday{background:#c6efce;}
    .empty{background:#f9f9f9;}

    .legend{font-size:11px;margin:8px 0;}
    .legend span{margin-right:12px;}

    .toolbar{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px;}
    .holiday-input{font-size:11px;}
    .holiday-input input{font-size:11px;padding:2px;}
    .holiday-input button, #exportPdf, #exportExcel{font-size:11px;padding:3px 6px;margin-left:4px;}

    @media print {
      .toolbar{display:none}
      body{background:#fff;}
      table{border-collapse:collapse;width:100%;font-size:10px;}
      th, td{border:1px solid #000;text-align:center;padding:2px;}
      thead th{background:#d9d9d9 !important;-webkit-print-color-adjust: exact;print-color-adjust: exact;}
      .month-cell{background:#cfd8dc !important;-webkit-print-color-adjust: exact;print-color-adjust: exact;}
      .month-row td{background:#e8eef3 !important;-webkit-print-color-adjust: exact;print-color-adjust: exact;}
      .month-row .daynum{background:#b0bec5 !important;-webkit-print-color-adjust: exact;print-color-adjust: exact;}
      .shift-name{background:#ddebf7 !important;-webkit-print-color-adjust: exact;print-color-adjust: exact;}
      .daynum{background:#f1f1f1 !important;-webkit-print-color-adjust: exact;print-color-adjust: exact;}
      .weekend{background:#fff2cc !important;-webkit-print-color-adjust: exact;print-color-adjust: exact;}
      .holiday{background:#c6efce !important;-webkit-print-color-adjust: exact;print-color-adjust: exact;}
      .empty{background:#f9f9f9 !important;-webkit-print-color-adjust: exact;print-color-adjust: exact;}
    }
  </style>
</head>
<body>
  <header id="pageHeader">
    <h1>SHUAIBA POWER AND WATER PRODUCTION STATIONS</h1>
    <h2>SHIFT SCHEDULE FOR OPERATION STAFF — <span id="headerYear"></span></h2>
    <div id="periodLabel"></div>
  </header>

  <div class="container">
    <div>
      <div class="toolbar">
        <div class="legend">
          <span><b>M</b> Morning</span>
          <span><b>A</b> Afternoon</span>
          <span><b>N</b> Night</span>
          <span><b>O</b> Off</span>
          <span style="background:#fff2cc;padding:2px 6px;">Fri/Sat</span>
          <span style="background:#c6efce;padding:2px 6px;">Holiday</span>
        </div>
        <div class="holiday-input">
          <label for="yearSelect">Year:</label>
          <input type="number" id="yearSelect" value="2025" min="2020" max="2100" style="width:70px;">
          <label for="monthsSelect">Months:</label>
          <input id="monthsSelect" value="1-12" style="width:80px;"/>
          <label for="holidayDates">Holidays:</label>
          <input id="holidayDates" placeholder="YYYY-MM-DD, …" />
          <button id="applySettings">Apply</button>
          <button id="exportPdf">Export PDF</button>
          <button id="exportExcel">Export Excel</button>
        </div>
      </div>

      <div class="table-wrap" id="tableWrap">
        <table id="shiftTable">
          <thead>
            <tr id="daysHeader">
              <th>Month</th>
            </tr>
          </thead>
          <tbody id="scheduleBody"></tbody>
        </table>
      </div>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
  <script>
    const shiftGroups = ['A‑Shift','B‑Shift','C‑Shift','D‑Shift','E‑Shift'];

    let holidayList = new Set([]);

    const refDate = new Date(2025, 8, 1);
    const startSequences = {
      'A‑Shift': ['O','M','A','N','O','O'],
      'B‑Shift': ['M','A','N','O','O'],
      'C‑Shift': ['O','O','M','A','N'],
      'D‑Shift': ['N','O','O','M','A'],
      'E‑Shift': ['A','N','O','O','M']
    };

    function ymd(y,m,d){ return `${y}-${String(m+1).padStart(2,'0')}-${String(d).padStart(2,'0')}`; }
    function isWeekend(date){ return date.getDay()===5 || date.getDay()===6; }
    function daysInMonth(y,m){ return new Date(y, m+1, 0).getDate(); }

    const scheduleBody = document.getElementById('scheduleBody');
    const daysHeader = document.getElementById('daysHeader');
    const periodLabel = document.getElementById('periodLabel');
    const headerYear = document.getElementById('headerYear');

    function makeHeader(){
      daysHeader.innerHTML = '<th>Month</th>';
      for(let d=1; d<=31; d++){
        const th = document.createElement('th');
        th.textContent = d;
        th.className = 'daynum';
        daysHeader.appendChild(th);
      }
    }

    function shortMonthName(m){
      return new Date(2000, m, 1).toLocaleString('en',{month:'short'}).toUpperCase() + '.';
    }

    function render(){
      const year = parseInt(document.getElementById('yearSelect').value,10);
      headerYear.textContent = year;

      const monthsInput = document.getElementById('monthsSelect').value.trim();
      let monthRange = [];
      if(monthsInput.includes('-')){
        let [start,end] = monthsInput.split('-').map(Number);
        for(let m=start; m<=end; m++) monthRange.push(m-1);
      } else {
        monthRange = monthsInput.split(',').map(v=>parseInt(v,10)-1);
      }

      periodLabel.textContent = monthRange.map(m=>shortMonthName(m)).join(' – ') + ` (${year})`;

      makeHeader();
      scheduleBody.innerHTML = '';
      monthRange.forEach((m) => {
        const days = daysInMonth(year,m);
        const name = shortMonthName(m);

        const monthRow = document.createElement('tr');
        monthRow.className = 'month-row';
        const monthCell = document.createElement('td');
        monthCell.textContent = name;
        monthCell.className = 'month-cell';
        monthRow.appendChild(monthCell);
        for(let d=1; d<=31; d++){
          const td = document.createElement('td');
          if(d<=days){ td.textContent = d; td.classList.add('daynum'); }
          else{ td.className = 'empty'; }
          monthRow.appendChild(td);
        }
        scheduleBody.appendChild(monthRow);

        shiftGroups.forEach((group) => {
          const r = document.createElement('tr');
          const nameCell = document.createElement('td');
          nameCell.textContent = group;
          nameCell.className = 'shift-name';
          r.appendChild(nameCell);

          for(let d=1; d<=31; d++){
            const td = document.createElement('td');
            if(d>days){ td.className = 'empty'; r.appendChild(td); continue; }
            const date = new Date(year, m, d);

            const offsetFromStart = Math.floor((date - refDate) / (1000*60*60*24));
            let sequence = startSequences[group];
            let symbol = sequence[(offsetFromStart % sequence.length + sequence.length) % sequence.length];

            let cls = [];
            if(isWeekend(date)) cls.push('weekend');
            if(holidayList.has(ymd(year,m,d))) cls.push('holiday');
            td.className = cls.join(' ');
            td.textContent = symbol;
            r.appendChild(td);
          }
          scheduleBody.appendChild(r);
        });
      });
    }

    document.getElementById('applySettings').addEventListener('click', () => {
      const raw = (document.getElementById('holidayDates').value || '').trim();
      const set = new Set();
      if(raw){ raw.split(/[ ,\n]+/).forEach(v => { if(/^\d{4}-\d{2}-\d{2}$/.test(v.trim())) set.add(v.trim()); }); }
      holidayList = set;
      render();
    });

    document.getElementById('exportPdf').addEventListener('click', async () => {
      const { jsPDF } = window.jspdf;
      const pdf = new jsPDF('l', 'pt', 'a2');

      const header = document.getElementById('pageHeader');
      const tableWrap = document.getElementById('tableWrap');

      const headerCanvas = await html2canvas(header, { scale: 2 });
      const headerImg = headerCanvas.toDataURL('image/png');
      const headerWidth = pdf.internal.pageSize.getWidth() - 40;
      const headerHeight = headerCanvas.height * headerWidth / headerCanvas.width;
      pdf.addImage(headerImg, 'PNG', 20, 20, headerWidth, headerHeight);

      const canvas = await html2canvas(tableWrap, { scale: 2, scrollY: -window.scrollY, windowWidth: tableWrap.scrollWidth, windowHeight: tableWrap.scrollHeight });
      const imgData = canvas.toDataURL('image/png');
      let pageWidth = pdf.internal.pageSize.getWidth();
      let imgWidth = pageWidth - 40;
      let imgHeight = canvas.height * imgWidth / canvas.width;

      let y = 40 + headerHeight;
      pdf.addImage(imgData, 'PNG', 20, y, imgWidth, imgHeight);

      pdf.save('ShiftSchedule.pdf');
    });

    document.getElementById('exportExcel').addEventListener('click', () => {
      const table = document.getElementById('shiftTable');
      const wb = XLSX.utils.table_to_book(table, {sheet:"Rota"});
      XLSX.writeFile(wb, 'ShiftSchedule.xlsx');
    });

    render();
  </script>
</body>
</html>
