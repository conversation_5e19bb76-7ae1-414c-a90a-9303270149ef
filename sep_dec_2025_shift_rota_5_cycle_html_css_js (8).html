<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Shift Schedule – Dynamic Yearly Rota</title>
  <style>
    body{font-family:Arial, sans-serif;color:#000;margin:0;background:#f5f5f5;padding:20px;}

    .header-container{
      display:flex;
      justify-content:space-between;
      align-items:center;
      margin-bottom:20px;
      background:#fff;
      padding:15px 20px;
      border:2px solid #000;
    }

    .header-text{
      text-align:center;
      flex:1;
    }

    .header-text h1{
      font-size:18px;
      font-weight:bold;
      margin:0 0 5px 0;
      letter-spacing:1px;
    }

    .header-text h2{
      font-size:14px;
      font-weight:bold;
      margin:0;
    }

    .year-box{
      border:2px solid #000;
      padding:10px 15px;
      font-size:24px;
      font-weight:bold;
      background:#fff;
      min-width:80px;
      text-align:center;
    }

    .container{
      width:100%;
      display:flex;
      justify-content:center;
    }

    table{
      border-collapse:collapse;
      font-size:8px;
      background:#fff;
      border:2px solid #000;
      margin:0 auto;
    }

    th, td{
      border:1px solid #000;
      text-align:center;
      padding:1px;
      width:22px;
      height:16px;
      vertical-align:middle;
    }

    .month-header{
      background:#e0e0e0;
      font-weight:bold;
      font-size:9px;
      width:90px;
      text-align:left;
      padding-left:8px;
      border-right:2px solid #000;
    }

    .day-header{
      background:#e0e0e0;
      font-weight:bold;
      font-size:7px;
      height:18px;
    }

    .shift-row{
      height:14px;
    }

    .shift-cell{
      font-size:7px;
      font-weight:bold;
    }

    .month-name-cell{
      background:#e0e0e0;
      font-weight:bold;
      font-size:9px;
      text-align:left;
      padding-left:8px;
      border-right:2px solid #000;
      border-bottom:2px solid #000;
    }

    .weekend{background:#FFEB9C;}
    .holiday{background:#92D050;}
    .empty{background:#f9f9f9;}

    .legend-container{
      display:flex;
      justify-content:center;
      margin-top:15px;
    }

    .legend{
      display:flex;
      gap:20px;
      font-size:12px;
      font-weight:bold;
    }

    .legend-item{
      display:flex;
      align-items:center;
      gap:5px;
    }

    .legend-box{
      width:20px;
      height:15px;
      border:1px solid #000;
    }

    .legend-fri-sat{background:#fff2cc;}
    .legend-holiday{background:#90EE90;}

    .toolbar{
      display:flex;
      justify-content:center;
      align-items:center;
      margin-bottom:15px;
      gap:10px;
    }

    .toolbar input, .toolbar button{
      font-size:11px;
      padding:5px;
    }

    @media print {
      body{background:#fff;padding:10px;}
      .toolbar{display:none;}
      table{font-size:8px;}
      .header-container{margin-bottom:15px;}
    }
  </style>
</head>
<body>
  <div class="header-container" id="pageHeader">
    <div class="header-text">
      <h1>SHUAIBA POWER AND WATER PRODUCTION STATIONS</h1>
      <h2>SHIFT SCHEDULE FOR OPERATION STAFF DURING YEAR <span id="headerYear"></span></h2>
    </div>
    <div class="year-box" id="yearBox">2025</div>
  </div>

  <div class="toolbar">
    <label for="yearSelect">Year:</label>
    <input type="number" id="yearSelect" value="2025" min="2020" max="2100">
    <label for="monthsSelect">Months:</label>
    <input id="monthsSelect" value="1-12">
    <label for="holidayDates">Holidays:</label>
    <input id="holidayDates" placeholder="YYYY-MM-DD, …">
    <button id="applySettings">Apply</button>
    <button id="exportPdf">Export PDF</button>
    <button id="exportExcel">Export Excel</button>
  </div>

  <div class="container">
    <div class="table-wrap" id="tableWrap">
      <table id="shiftTable">
        <thead>
          <tr id="daysHeader"></tr>
        </thead>
        <tbody id="scheduleBody"></tbody>
      </table>
    </div>
  </div>

  <div class="legend-container">
    <div class="legend">
      <div class="legend-item">
        <div class="legend-box legend-fri-sat"></div>
        <span>FRIDAY<br>& SATURDAY</span>
      </div>
      <div class="legend-item">
        <div class="legend-box legend-holiday"></div>
        <span>HOLIDAY</span>
      </div>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
  <script>
    const shiftGroups = ['A‑Shift','B‑Shift','C‑Shift','D‑Shift','E‑Shift'];

    let holidayList = new Set([]);

    const refDate = new Date(2025, 8, 1);
    const startSequences = {
      'A‑Shift': ['O','M','A','N','O','O'],
      'B‑Shift': ['M','A','N','O','O'],
      'C‑Shift': ['O','O','M','A','N'],
      'D‑Shift': ['N','O','O','M','A'],
      'E‑Shift': ['A','N','O','O','M']
    };

    function ymd(y,m,d){ return `${y}-${String(m+1).padStart(2,'0')}-${String(d).padStart(2,'0')}`; }
    function isWeekend(date){ return date.getDay()===5 || date.getDay()===6; }
    function daysInMonth(y,m){ return new Date(y, m+1, 0).getDate(); }

    const scheduleBody = document.getElementById('scheduleBody');
    const daysHeader = document.getElementById('daysHeader');
    const headerYear = document.getElementById('headerYear');

    function makeHeader(){
      daysHeader.innerHTML = '<th class="month-header"></th>';
      for(let d=1; d<=31; d++){
        const th = document.createElement('th');
        th.textContent = d;
        th.className = 'day-header';
        daysHeader.appendChild(th);
      }
    }

    function getMonthName(m){
      const months = ['JANUARY','FEBRUARY','MARCH','APRIL','MAY','JUNE',
                     'JULY','AUGUST','SEPTEMBER','OCTOBER','NOVEMBER','DECEMBER'];
      return months[m];
    }

    function render(){
      const year = parseInt(document.getElementById('yearSelect').value,10);
      headerYear.textContent = year;
      document.getElementById('yearBox').textContent = year;

      const monthsInput = document.getElementById('monthsSelect').value.trim();
      let monthRange = [];
      if(monthsInput.includes('-')){
        let [start,end] = monthsInput.split('-').map(Number);
        for(let m=start; m<=end; m++) monthRange.push(m-1);
      } else {
        monthRange = monthsInput.split(',').map(v=>parseInt(v,10)-1);
      }

      makeHeader();
      scheduleBody.innerHTML = '';

      monthRange.forEach((m) => {
        const days = daysInMonth(year,m);
        const monthName = getMonthName(m);

        // Month name row
        const monthRow = document.createElement('tr');
        const monthCell = document.createElement('td');
        monthCell.textContent = monthName;
        monthCell.className = 'month-name-cell';
        monthRow.appendChild(monthCell);

        for(let d=1; d<=31; d++){
          const td = document.createElement('td');
          if(d<=days){
            td.textContent = d;
            const date = new Date(year, m, d);
            let cls = ['day-header'];
            if(isWeekend(date)) cls.push('weekend');
            if(holidayList.has(ymd(year,m,d))) cls.push('holiday');
            td.className = cls.join(' ');
          } else {
            td.className = 'empty';
          }
          monthRow.appendChild(td);
        }
        scheduleBody.appendChild(monthRow);

        // Shift rows for this month
        shiftGroups.forEach((group) => {
          const r = document.createElement('tr');
          r.className = 'shift-row';

          const nameCell = document.createElement('td');
          nameCell.textContent = group;
          nameCell.className = 'month-header';
          r.appendChild(nameCell);

          for(let d=1; d<=31; d++){
            const td = document.createElement('td');
            if(d>days){
              td.className = 'empty';
              r.appendChild(td);
              continue;
            }

            const date = new Date(year, m, d);
            const offsetFromStart = Math.floor((date - refDate) / (1000*60*60*24));
            let sequence = startSequences[group];
            let symbol = sequence[(offsetFromStart % sequence.length + sequence.length) % sequence.length];

            let cls = ['shift-cell'];
            if(isWeekend(date)) cls.push('weekend');
            if(holidayList.has(ymd(year,m,d))) cls.push('holiday');
            td.className = cls.join(' ');
            td.textContent = symbol;
            r.appendChild(td);
          }
          scheduleBody.appendChild(r);
        });
      });
    }

    document.getElementById('applySettings').addEventListener('click', () => {
      const raw = (document.getElementById('holidayDates').value || '').trim();
      const set = new Set();
      if(raw){ raw.split(/[ ,\n]+/).forEach(v => { if(/^\d{4}-\d{2}-\d{2}$/.test(v.trim())) set.add(v.trim()); }); }
      holidayList = set;
      render();
    });

    document.getElementById('exportPdf').addEventListener('click', async () => {
      const { jsPDF } = window.jspdf;
      const pdf = new jsPDF('l', 'pt', 'a2');

      const header = document.getElementById('pageHeader');
      const tableWrap = document.getElementById('tableWrap');

      const headerCanvas = await html2canvas(header, { scale: 2 });
      const headerImg = headerCanvas.toDataURL('image/png');
      const headerWidth = pdf.internal.pageSize.getWidth() - 40;
      const headerHeight = headerCanvas.height * headerWidth / headerCanvas.width;
      pdf.addImage(headerImg, 'PNG', 20, 20, headerWidth, headerHeight);

      const canvas = await html2canvas(tableWrap, { scale: 2, scrollY: -window.scrollY, windowWidth: tableWrap.scrollWidth, windowHeight: tableWrap.scrollHeight });
      const imgData = canvas.toDataURL('image/png');
      let pageWidth = pdf.internal.pageSize.getWidth();
      let imgWidth = pageWidth - 40;
      let imgHeight = canvas.height * imgWidth / canvas.width;

      let y = 40 + headerHeight;
      pdf.addImage(imgData, 'PNG', 20, y, imgWidth, imgHeight);

      pdf.save('ShiftSchedule.pdf');
    });

    document.getElementById('exportExcel').addEventListener('click', () => {
      const table = document.getElementById('shiftTable');
      const wb = XLSX.utils.table_to_book(table, {sheet:"Rota"});
      XLSX.writeFile(wb, 'ShiftSchedule.xlsx');
    });

    render();
  </script>
</body>
</html>
